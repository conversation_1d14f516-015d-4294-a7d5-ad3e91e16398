-- <PERSON><PERSON>t to clean up old RLS policies
-- Run this in the Supabase web portal BEFORE running the migration_expand_pet_roles.sql
-- You can run each DROP statement individually if needed

-- Clean up old profiles policies
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON profiles;

-- Clean up old pets policies
DROP POLICY IF EXISTS "Users can view pets they own or have access to" ON pets;
DROP POLICY IF EXISTS "Users can create pets" ON pets;
DROP POLICY IF EXISTS "Pet creators and owners can update pets" ON pets;
DROP POLICY IF EXISTS "Pet creators can delete pets" ON pets;
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON pets;
DROP POLICY IF EXISTS "pets_select_policy" ON pets;
DROP POLICY IF EXISTS "pets_insert_policy" ON pets;
DROP POLICY IF EXISTS "pets_update_policy" ON pets;
DROP POLICY IF EXISTS "pets_delete_policy" ON pets;

-- Clean up old pet_owners policies
DROP POLICY IF EXISTS "Users can view pet ownership for accessible pets" ON pet_owners;
DROP POLICY IF EXISTS "Pet creators can manage pet ownership" ON pet_owners;
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON pet_owners;
DROP POLICY IF EXISTS "pet_owners_select_policy" ON pet_owners;
DROP POLICY IF EXISTS "pet_owners_insert_policy" ON pet_owners;
DROP POLICY IF EXISTS "pet_owners_update_policy" ON pet_owners;
DROP POLICY IF EXISTS "pet_owners_delete_policy" ON pet_owners;

-- Clean up old feeding_logs policies
DROP POLICY IF EXISTS "Users can view feeding logs for accessible pets" ON feeding_logs;
DROP POLICY IF EXISTS "Users can create feeding logs for accessible pets" ON feeding_logs;
DROP POLICY IF EXISTS "Users can update own feeding logs" ON feeding_logs;
DROP POLICY IF EXISTS "Users can delete own feeding logs" ON feeding_logs;
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON feeding_logs;
DROP POLICY IF EXISTS "feeding_logs_policy" ON feeding_logs;
DROP POLICY IF EXISTS "feeding_logs_select_policy" ON feeding_logs;
DROP POLICY IF EXISTS "feeding_logs_insert_policy" ON feeding_logs;
DROP POLICY IF EXISTS "feeding_logs_update_policy" ON feeding_logs;
DROP POLICY IF EXISTS "feeding_logs_delete_policy" ON feeding_logs;

-- Clean up old litter_logs policies
DROP POLICY IF EXISTS "Users can view litter logs for accessible pets" ON litter_logs;
DROP POLICY IF EXISTS "Users can create litter logs for accessible pets" ON litter_logs;
DROP POLICY IF EXISTS "Users can update own litter logs" ON litter_logs;
DROP POLICY IF EXISTS "Users can delete own litter logs" ON litter_logs;
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON litter_logs;
DROP POLICY IF EXISTS "litter_logs_policy" ON litter_logs;
DROP POLICY IF EXISTS "litter_logs_select_policy" ON litter_logs;
DROP POLICY IF EXISTS "litter_logs_insert_policy" ON litter_logs;
DROP POLICY IF EXISTS "litter_logs_update_policy" ON litter_logs;
DROP POLICY IF EXISTS "litter_logs_delete_policy" ON litter_logs;

-- Clean up old vaccinations policies
DROP POLICY IF EXISTS "Users can view vaccinations for accessible pets" ON vaccinations;
DROP POLICY IF EXISTS "Users can create vaccinations for accessible pets" ON vaccinations;
DROP POLICY IF EXISTS "Users can update own vaccinations" ON vaccinations;
DROP POLICY IF EXISTS "Users can delete own vaccinations" ON vaccinations;
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON vaccinations;
DROP POLICY IF EXISTS "vaccinations_policy" ON vaccinations;
DROP POLICY IF EXISTS "vaccinations_select_policy" ON vaccinations;
DROP POLICY IF EXISTS "vaccinations_insert_policy" ON vaccinations;
DROP POLICY IF EXISTS "vaccinations_update_policy" ON vaccinations;
DROP POLICY IF EXISTS "vaccinations_delete_policy" ON vaccinations;

-- Clean up old notes policies
DROP POLICY IF EXISTS "Users can view notes for accessible pets" ON notes;
DROP POLICY IF EXISTS "Users can create notes for accessible pets" ON notes;
DROP POLICY IF EXISTS "Users can update own notes" ON notes;
DROP POLICY IF EXISTS "Users can delete own notes" ON notes;
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON notes;
DROP POLICY IF EXISTS "notes_policy" ON notes;
DROP POLICY IF EXISTS "notes_select_policy" ON notes;
DROP POLICY IF EXISTS "notes_insert_policy" ON notes;
DROP POLICY IF EXISTS "notes_update_policy" ON notes;
DROP POLICY IF EXISTS "notes_delete_policy" ON notes;

-- Clean up old triggers
DROP TRIGGER IF EXISTS on_pet_created ON pets;

SELECT 'Old policies and triggers cleaned up' as status;
