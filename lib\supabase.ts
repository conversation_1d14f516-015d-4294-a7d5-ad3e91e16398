import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      pets: {
        Row: {
          id: string
          name: string
          nickname: string | null
          species: string
          breed: string | null
          date_of_birth: string | null
          weight: string | null
          color: string | null
          notes: string | null
          image_url: string | null
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          nickname?: string | null
          species: string
          breed?: string | null
          date_of_birth?: string | null
          weight?: string | null
          color?: string | null
          notes?: string | null
          image_url?: string | null
          created_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          nickname?: string | null
          species?: string
          breed?: string | null
          date_of_birth?: string | null
          weight?: string | null
          color?: string | null
          notes?: string | null
          image_url?: string | null
          created_by?: string
          created_at?: string
          updated_at?: string
        }
      }
      pet_owners: {
        Row: {
          id: string
          pet_id: string
          user_id: string
          role: 'owner' | 'caretaker' | 'temporary_caretaker'
          created_at: string
        }
        Insert: {
          id?: string
          pet_id: string
          user_id: string
          role: 'owner' | 'caretaker' | 'temporary_caretaker'
          created_at?: string
        }
        Update: {
          id?: string
          pet_id?: string
          user_id?: string
          role?: 'owner' | 'caretaker' | 'temporary_caretaker'
          created_at?: string
        }
      }
      feeding_logs: {
        Row: {
          id: string
          pet_id: string
          user_id: string
          food_type: string
          amount: string | null
          notes: string | null
          timestamp: string
          created_at: string
        }
        Insert: {
          id?: string
          pet_id: string
          user_id: string
          food_type: string
          amount?: string | null
          notes?: string | null
          timestamp: string
          created_at?: string
        }
        Update: {
          id?: string
          pet_id?: string
          user_id?: string
          food_type?: string
          amount?: string | null
          notes?: string | null
          timestamp?: string
          created_at?: string
        }
      }
      litter_logs: {
        Row: {
          id: string
          pet_id: string
          user_id: string
          type: 'scoop' | 'deep-clean' | 'full-change'
          notes: string | null
          timestamp: string
          created_at: string
        }
        Insert: {
          id?: string
          pet_id: string
          user_id: string
          type: 'scoop' | 'deep-clean' | 'full-change'
          notes?: string | null
          timestamp: string
          created_at?: string
        }
        Update: {
          id?: string
          pet_id?: string
          user_id?: string
          type?: 'scoop' | 'deep-clean' | 'full-change'
          notes?: string | null
          timestamp?: string
          created_at?: string
        }
      }
      vaccinations: {
        Row: {
          id: string
          pet_id: string
          user_id: string
          vaccine: string
          date: string
          next_due: string | null
          veterinarian: string
          lot_number: string | null
          notes: string | null
          created_at: string
        }
        Insert: {
          id?: string
          pet_id: string
          user_id: string
          vaccine: string
          date: string
          next_due?: string | null
          veterinarian: string
          lot_number?: string | null
          notes?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          pet_id?: string
          user_id?: string
          vaccine?: string
          date?: string
          next_due?: string | null
          veterinarian?: string
          lot_number?: string | null
          notes?: string | null
          created_at?: string
        }
      }
      notes: {
        Row: {
          id: string
          pet_id: string
          user_id: string
          topic: string
          category: string
          description: string
          timestamp: string
          created_at: string
        }
        Insert: {
          id?: string
          pet_id: string
          user_id: string
          topic: string
          category: string
          description: string
          timestamp: string
          created_at?: string
        }
        Update: {
          id?: string
          pet_id?: string
          user_id?: string
          topic?: string
          category?: string
          description?: string
          timestamp?: string
          created_at?: string
        }
      }
      pet_share_tokens: {
        Row: {
          id: string
          token: string
          pet_id: string
          created_by: string
          expires_at: string
          is_active: boolean
          usage_count: number
          max_uses: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          token: string
          pet_id: string
          created_by: string
          expires_at: string
          is_active?: boolean
          usage_count?: number
          max_uses?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          token?: string
          pet_id?: string
          created_by?: string
          expires_at?: string
          is_active?: boolean
          usage_count?: number
          max_uses?: number | null
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type Inserts<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type Updates<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
