# Pet Roles Expansion

This update expands the pet ownership system to support multiple role types and fixes the pet sharing functionality.

## New Role Types

The system now supports three role types for pet ownership:

1. **Owner** - Full access to the pet, can share with others, can modify all data
2. **Caretaker** - Can view pet data and add records (feeding, litter, vaccinations, notes)
3. **Temporary Caretaker** - Limited time access, same permissions as Caretaker but intended for short-term care

## Database Changes

### Schema Updates
- Updated `pet_owners.role` constraint to include `'temporary_caretaker'`
- All existing `'caretaker'` roles remain unchanged

### New RLS Policies
The migration creates new policies with `_v2` suffixes to avoid conflicts:
- `pets_select_accessible_v2` - Users can view pets they have access to
- `pet_owners_insert_sharing_v2` - **Key fix**: Allows pet creators to share with anyone
- All other policies updated with proper access controls

## Migration Steps

### Step 1: Clean Up Old Policies (Optional but Recommended)
Run `supabase/cleanup_old_policies.sql` in your Supabase SQL Editor to remove old policies.

### Step 2: Apply Role Expansion
Run `supabase/migration_expand_pet_roles.sql` in your Supabase SQL Editor.

## Key Fixes

### Pet Sharing Issue Resolution
The main issue was in the `pet_owners` INSERT policy. The old policy prevented pet creators from inserting records where someone else was the `user_id`. 

**Old problematic policy:**
```sql
CREATE POLICY "pet_owners_insert_policy" ON pet_owners
  FOR INSERT WITH CHECK (
    EXISTS(SELECT 1 FROM pets WHERE id = pet_id AND created_by = auth.uid()) OR
    (user_id = auth.uid() AND EXISTS(SELECT 1 FROM pets WHERE id = pet_id AND created_by = auth.uid()))
  );
```

**New fixed policy:**
```sql
CREATE POLICY "pet_owners_insert_sharing_v2" ON pet_owners
  FOR INSERT WITH CHECK (
    EXISTS(SELECT 1 FROM pets WHERE id = pet_id AND created_by = auth.uid())
  );
```

This allows pet creators to grant access to anyone while maintaining security.

## Application Updates

### TypeScript Types
- Updated `lib/supabase.ts` to include `'temporary_caretaker'` in role types
- Updated `lib/storage.ts` sharePet method signature
- Updated `components/pet-manager.tsx` to show new role option

### UI Changes
The pet sharing dialog now includes:
- **Caretaker** (can view and add records)
- **Temporary Caretaker** (limited time access)

## Testing the Fix

After running the migration:

1. **Test Pet Sharing**: Try sharing a pet via email - should work without errors
2. **Test Role Assignment**: Verify different roles can be assigned
3. **Test Access Control**: Ensure users can only access pets they have permissions for
4. **Test Link Sharing**: Share tokens should still work (defaults to 'caretaker')

## Security Notes

- Pet creators can share their pets with anyone
- Non-creators cannot grant access to pets they don't own
- All role types have the same data access permissions (view + add records)
- Only pet creators can delete pets
- Users can only modify records they created

## Rollback Plan

If issues occur, you can:
1. Run the old policies from `supabase/schema.sql`
2. Revert the role constraint: `ALTER TABLE pet_owners ADD CONSTRAINT pet_owners_role_check CHECK (role IN ('owner', 'caretaker'));`

## Future Enhancements

Consider adding:
- Time-based expiration for temporary caretakers
- Different permission levels per role
- Bulk role management
- Role change notifications
