-- Migration to expand pet ownership roles and fix sharing policies
-- This can be run directly in the Supabase web portal
-- Run each section separately if needed

-- Step 1: Add new role options to the pet_owners table
-- First, remove the old constraint
ALTER TABLE pet_owners DROP CONSTRAINT IF EXISTS pet_owners_role_check;

-- Add the new constraint with expanded roles
ALTER TABLE pet_owners ADD CONSTRAINT pet_owners_role_check 
CHECK (role IN ('owner', 'caretaker', 'temporary_caretaker'));

-- Step 2: Update existing 'caretaker' roles to be more specific if needed
-- (Optional - you can run this if you want to migrate existing data)
-- UPDATE pet_owners SET role = 'caretaker' WHERE role = 'caretaker';

-- Step 3: Create new RLS policies with unique names to avoid conflicts
-- These will replace the existing policies

-- Profiles policies (keep existing ones, these are just for reference)
CREATE POLICY "profiles_select_own" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Helper function for pet access (recreate to ensure it exists)
CREATE OR REPLACE FUNCTION public.user_has_pet_access(pet_id_param UUID, user_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
  has_access BOOLEAN := FALSE;
BEGIN
  -- Check if user created the pet
  SELECT EXISTS(
    SELECT 1 FROM pets 
    WHERE id = pet_id_param AND created_by = user_id_param
  ) INTO has_access;
  
  -- If not creator, check pet_owners table
  IF NOT has_access THEN
    SELECT EXISTS(
      SELECT 1 FROM pet_owners 
      WHERE pet_id = pet_id_param AND user_id = user_id_param
    ) INTO has_access;
  END IF;
  
  RETURN has_access;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Pet policies with new names
CREATE POLICY "pets_select_accessible_v2" ON pets
  FOR SELECT USING (
    public.user_has_pet_access(id, auth.uid())
  );

CREATE POLICY "pets_insert_own_v2" ON pets
  FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "pets_update_accessible_v2" ON pets
  FOR UPDATE USING (
    created_by = auth.uid() OR
    EXISTS(
      SELECT 1 FROM pet_owners 
      WHERE pet_id = id AND user_id = auth.uid() AND role IN ('owner', 'caretaker', 'temporary_caretaker')
    )
  );

CREATE POLICY "pets_delete_creator_only_v2" ON pets
  FOR DELETE USING (created_by = auth.uid());

-- Pet_owners policies with new names - these are the key ones for sharing
CREATE POLICY "pet_owners_select_accessible_v2" ON pet_owners
  FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS(SELECT 1 FROM pets WHERE id = pet_id AND created_by = auth.uid())
  );

-- This is the critical policy for sharing - allows pet creators to grant access to anyone
CREATE POLICY "pet_owners_insert_sharing_v2" ON pet_owners
  FOR INSERT WITH CHECK (
    EXISTS(SELECT 1 FROM pets WHERE id = pet_id AND created_by = auth.uid())
  );

CREATE POLICY "pet_owners_update_by_creator_v2" ON pet_owners
  FOR UPDATE USING (
    EXISTS(SELECT 1 FROM pets WHERE id = pet_id AND created_by = auth.uid())
  );

CREATE POLICY "pet_owners_delete_by_creator_v2" ON pet_owners
  FOR DELETE USING (
    EXISTS(SELECT 1 FROM pets WHERE id = pet_id AND created_by = auth.uid())
  );

-- Feeding logs policies with new names
CREATE POLICY "feeding_logs_select_accessible_v2" ON feeding_logs
  FOR SELECT USING (
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "feeding_logs_insert_accessible_v2" ON feeding_logs
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "feeding_logs_update_own_v2" ON feeding_logs
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "feeding_logs_delete_own_v2" ON feeding_logs
  FOR DELETE USING (user_id = auth.uid());

-- Litter logs policies with new names
CREATE POLICY "litter_logs_select_accessible_v2" ON litter_logs
  FOR SELECT USING (
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "litter_logs_insert_accessible_v2" ON litter_logs
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "litter_logs_update_own_v2" ON litter_logs
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "litter_logs_delete_own_v2" ON litter_logs
  FOR DELETE USING (user_id = auth.uid());

-- Vaccinations policies with new names
CREATE POLICY "vaccinations_select_accessible_v2" ON vaccinations
  FOR SELECT USING (
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "vaccinations_insert_accessible_v2" ON vaccinations
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "vaccinations_update_own_v2" ON vaccinations
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "vaccinations_delete_own_v2" ON vaccinations
  FOR DELETE USING (user_id = auth.uid());

-- Notes policies with new names
CREATE POLICY "notes_select_accessible_v2" ON notes
  FOR SELECT USING (
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "notes_insert_accessible_v2" ON notes
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "notes_update_own_v2" ON notes
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "notes_delete_own_v2" ON notes
  FOR DELETE USING (user_id = auth.uid());

-- Ensure RLS is enabled on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE pets ENABLE ROW LEVEL SECURITY;
ALTER TABLE pet_owners ENABLE ROW LEVEL SECURITY;
ALTER TABLE feeding_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE litter_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE vaccinations ENABLE ROW LEVEL SECURITY;
ALTER TABLE notes ENABLE ROW LEVEL SECURITY;

-- Ensure the pet ownership trigger is working
CREATE OR REPLACE FUNCTION public.handle_new_pet()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.pet_owners (pet_id, user_id, role)
  VALUES (NEW.id, NEW.created_by, 'owner');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_pet_created_v2
  AFTER INSERT ON pets
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_pet();

SELECT 'Pet roles expanded and sharing policies updated' as status;
